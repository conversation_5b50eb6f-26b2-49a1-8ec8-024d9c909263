import { useState, useEffect, useCallback } from 'react';
import { quizService } from '@/services/quizService';

export function useRapidReview(userId: string, topics?: string[]) {
  const [questions, setQuestions] = useState<any[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [answers, setAnswers] = useState<{ questionId: string; correct: boolean }[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [sessionComplete, setSessionComplete] = useState(false);

  useEffect(() => {
    async function fetchQuestions() {
      setIsLoading(true);
      setError(null);
      try {
        const data = await quizService.getRapidReviewQuestions(userId, topics);
        setQuestions(data);
        setCurrentIndex(0);
        setAnswers([]);
        setSessionComplete(false);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to load rapid review questions'));
      } finally {
        setIsLoading(false);
      }
    }
    if (userId) fetchQuestions();
  }, [userId, topics]);

  const answerQuestion = useCallback((questionId: string, correct: boolean) => {
    setAnswers(prev => [...prev, { questionId, correct }]);
    if (currentIndex + 1 < questions.length) {
      setCurrentIndex(idx => idx + 1);
    } else {
      setSessionComplete(true);
    }
  }, [currentIndex, questions.length]);

  const resetSession = useCallback(() => {
    setCurrentIndex(0);
    setAnswers([]);
    setSessionComplete(false);
  }, []);

  return {
    questions,
    currentIndex,
    currentQuestion: questions[currentIndex],
    answers,
    isLoading,
    error,
    sessionComplete,
    answerQuestion,
    resetSession,
  };
} 