import { getAuthToken } from '@/lib/auth';

class QuizService {
  private readonly baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
  }

  private async fetchWithAuth(endpoint: string) {
    const token = await getAuthToken();
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    if (!response.ok) {
      throw new Error(`Quiz API error: ${response.statusText}`);
    }
    return response.json();
  }

  async getRapidReviewQuestions(userId: string, topics?: string[]): Promise<any[]> {
    let url = `/quiz/rapid-review/${userId}`;
    if (topics && topics.length > 0) {
      url += `?topics=${encodeURIComponent(topics.join(','))}`;
    }
    return this.fetchWithAuth(url);
  }
}
export const quizService = new QuizService(); 