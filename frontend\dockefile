# frontend/Dockerfile
FROM node:20-alpine AS builder

WORKDIR /app
COPY . .

RUN npm install --frozen-lockfile
RUN npm run build

FROM node:20-alpine AS runner
WORKDIR /app

ENV NODE_ENV=production

COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/package.json ./package.json

RUN npm install --omit=dev --frozen-lockfile

EXPOSE 3000
CMD ["npm", "start"]
