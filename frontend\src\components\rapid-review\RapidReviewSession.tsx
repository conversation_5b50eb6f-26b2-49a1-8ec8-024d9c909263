import React, { useState } from 'react';
import { useRapidReview } from '@/hooks/useRapidReview';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';

interface RapidReviewSessionProps {
  userId: string;
  topics?: string[];
}

export function RapidReviewSession({ userId, topics }: RapidReviewSessionProps) {
  const {
    questions,
    currentIndex,
    currentQuestion,
    answers,
    isLoading,
    error,
    sessionComplete,
    answerQuestion,
    resetSession,
  } = useRapidReview(userId, topics);

  const [showAnswer, setShowAnswer] = useState(false);

  if (isLoading) {
    return <div className="text-center text-blue-500">Loading rapid review questions...</div>;
  }
  if (error) {
    return <div className="text-center text-red-500">{error.message}</div>;
  }
  if (!currentQuestion && !sessionComplete) {
    return <div className="text-center text-gray-500">No questions available for rapid review.</div>;
  }

  if (sessionComplete) {
    const correctCount = answers.filter(a => a.correct).length;
    const missed = questions.filter(q => !answers.find(a => a.questionId === q.id && a.correct));
    return (
      <div className="text-center space-y-4">
        <h2 className="text-2xl font-bold text-green-700">Session Complete!</h2>
        <div className="text-lg">Score: {correctCount} / {questions.length}</div>
        {missed.length > 0 && (
          <div>
            <div className="font-semibold mb-2">Missed Questions:</div>
            <ul className="text-left inline-block">
              {missed.map(q => (
                <li key={q.id} className="mb-1">- {q.question}</li>
              ))}
            </ul>
          </div>
        )}
        <Button onClick={resetSession} className="mt-4">Retry</Button>
      </div>
    );
  }

  // MCQ or short answer logic
  const isMCQ = currentQuestion.options && Array.isArray(currentQuestion.options);

  return (
    <div className="w-full max-w-lg mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-500">Question {currentIndex + 1} / {questions.length}</div>
        <Progress value={((currentIndex + 1) / questions.length) * 100} className="w-32" />
      </div>
      <div className="bg-white rounded-xl shadow p-6">
        <div className="mb-4">
          <h2 className="text-lg font-semibold">{currentQuestion.question}</h2>
          {currentQuestion.topic && (
            <div className="text-xs text-blue-500 mt-1">Topic: {currentQuestion.topic}</div>
          )}
        </div>
        {isMCQ ? (
          <div className="space-y-2">
            {currentQuestion.options.map((opt: string, idx: number) => (
              <Button
                key={opt}
                className="w-full text-left"
                variant="outline"
                onClick={() => {
                  const correct = opt === currentQuestion.correct_answer;
                  answerQuestion(currentQuestion.id, correct);
                  setShowAnswer(false);
                }}
              >
                {String.fromCharCode(65 + idx)}. {opt}
              </Button>
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            {showAnswer ? (
              <div className="p-4 bg-blue-50 rounded text-blue-700 mb-2">
                <div className="font-semibold">Answer:</div>
                <div>{currentQuestion.correct_answer}</div>
              </div>
            ) : null}
            <Button className="w-full" onClick={() => setShowAnswer(true)}>
              Show Answer
            </Button>
            <div className="flex space-x-2 mt-2">
              <Button
                variant="outline"
                onClick={() => {
                  answerQuestion(currentQuestion.id, false);
                  setShowAnswer(false);
                }}
              >
                I got it wrong
              </Button>
              <Button
                className="bg-green-600 hover:bg-green-700 text-white"
                onClick={() => {
                  answerQuestion(currentQuestion.id, true);
                  setShowAnswer(false);
                }}
              >
                I got it right
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 