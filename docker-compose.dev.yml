version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: medtrack-postgres-dev
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-medtrack_dev}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres-dev-data:/var/lib/postgresql/data
      - ./backend/migrations:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped
    networks:
      - app-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: medtrack-redis-dev
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis123}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis-dev-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped
    networks:
      - app-network

  # Backend API (Development)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: medtrack-backend-dev
    ports:
      - "${API_PORT:-3002}:3002"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-medtrack_dev}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379
      - JWT_SECRET=${JWT_SECRET:-dev-jwt-secret-key}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET:-dev-jwt-refresh-secret-key}
      - PORT=3002
      - TYPEORM_SYNC=${TYPEORM_SYNC:-true}
      - TYPEORM_LOGGING=${TYPEORM_LOGGING:-true}
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - app-network

  # Python Analytics Service (Development)
  analytics:
    build:
      context: ./backend/python_analytics
      dockerfile: Dockerfile.dev
    container_name: medtrack-analytics-dev
    ports:
      - "${ANALYTICS_PORT:-5000}:5000"
      - "5678:5678"  # Debug port
    environment:
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-medtrack_dev}
      - JWT_SECRET=${JWT_SECRET:-dev-jwt-secret-key}
      - PORT=5000
    volumes:
      - ./backend/python_analytics:/app
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - app-network

  # Frontend (Development)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: medtrack-frontend-dev
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:3002
      - NEXT_PUBLIC_ANALYTICS_URL=http://localhost:5000
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
      - analytics
    restart: unless-stopped
    networks:
      - app-network

  # Adminer for database management
  adminer:
    image: adminer:latest
    container_name: medtrack-adminer
    ports:
      - "8081:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - app-network

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: medtrack-redis-commander
    ports:
      - "8082:8081"
    environment:
      REDIS_HOSTS: local:redis:6379:0:${REDIS_PASSWORD:-redis123}
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - app-network

volumes:
  postgres-dev-data:
  redis-dev-data:

networks:
  app-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: ************/24
