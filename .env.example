# Environment Variables Template
# Copy this file to .env and fill in your actual values
# NEVER commit .env files to version control

# ===========================================
# APPLICATION CONFIGURATION
# ===========================================
NODE_ENV=development
PORT=3000

# ===========================================
# SERVICE PORTS
# ===========================================
FRONTEND_PORT=3000
BACKEND_PORT=3002
ANALYTICS_PORT=5000
POSTGRES_PORT=5432
REDIS_PORT=6379

# ===========================================
# DATABASE CONFIGURATION
# ===========================================
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_postgres_password_here
POSTGRES_DB=medtrack
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}

# ===========================================
# REDIS CONFIGURATION
# ===========================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}

# ===========================================
# JWT SECRETS (Generate strong random strings)
# ===========================================
JWT_SECRET=your_very_secure_jwt_secret_at_least_32_characters_long
JWT_REFRESH_SECRET=your_very_secure_jwt_refresh_secret_at_least_32_characters_long

# ===========================================
# TYPEORM CONFIGURATION
# ===========================================
TYPEORM_SYNC=true
TYPEORM_LOGGING=true

# ===========================================
# AI CONFIGURATION
# ===========================================
CLAUDE_API_KEY=your_claude_api_key_here
CLAUDE_MODEL=claude-3-5-sonnet-20241022
CLAUDE_MAX_TOKENS=4096

# ===========================================
# FRONTEND CONFIGURATION
# ===========================================
NEXT_PUBLIC_API_URL=http://localhost:3002
NEXT_PUBLIC_ANALYTICS_API_URL=http://localhost:5000

# ===========================================
# PRODUCTION OVERRIDES
# ===========================================
# Uncomment and modify for production deployment
# NODE_ENV=production
# TYPEORM_SYNC=false
# TYPEORM_LOGGING=false
# NEXT_PUBLIC_API_URL=https://your-api-domain.com
# NEXT_PUBLIC_ANALYTICS_API_URL=https://your-analytics-domain.com
